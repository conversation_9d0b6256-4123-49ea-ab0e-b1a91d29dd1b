package feedback_test

import (
	"testing"

	"langchain.com/smith/feedback"
	"langchain.com/smith/util"

	"github.com/stretchr/testify/assert"
)

func TestGetDefaultFeedbackConfig(t *testing.T) {
	t.Run("empty slice -> continuous default", func(t *testing.T) {
		config := feedback.GetDefaultFeedbackConfig([]feedback.FeedbackCreateSchema{})
		assert.Equal(t, feedback.FeedbackTypeContinuous, config.Type)
		assert.Nil(t, config.Min)
		assert.Nil(t, config.Max)
		assert.Empty(t, config.Categories)
	})

	t.Run("all correctness with score -> continuous [0,1]", func(t *testing.T) {
		feedbacks := []feedback.FeedbackCreateSchema{
			{Feedback: feedback.Feedback{Key: "correctness", Score: float64(0.5)}},
			{Feedback: feedback.Feedback{Key: " Correctness ", Score: float64(1.0)}},
			{Feedback: feedback.Feedback{Key: "CORRECTNESS", Score: float64(0)}},
		}
		config := feedback.GetDefaultFeedbackConfig(feedbacks)

		assert.Equal(t, feedback.FeedbackTypeContinuous, config.Type)
		assert.Equal(t, float64(0), *config.Min)
		assert.Equal(t, float64(1), *config.Max)

		// Expect two categories: value=0, value=1
		assert.Len(t, config.Categories, 2)
		assert.Equal(t, float64(0), config.Categories[0].Value)
		assert.Equal(t, float64(1), config.Categories[1].Value)
	})

	t.Run("all note -> freeform", func(t *testing.T) {
		feedbacks := []feedback.FeedbackCreateSchema{
			{Feedback: feedback.Feedback{Key: "Note"}},
			{Feedback: feedback.Feedback{Key: " note "}},
			{Feedback: feedback.Feedback{Key: "NOTE", Score: nil}},
		}
		config := feedback.GetDefaultFeedbackConfig(feedbacks)
		assert.Equal(t, feedback.FeedbackTypeFreeform, config.Type)
		assert.Empty(t, config.Categories)
	})

	t.Run("mixed -> continuous default", func(t *testing.T) {
		feedbacks := []feedback.FeedbackCreateSchema{
			{Feedback: feedback.Feedback{Key: "correctness", Score: float64(1)}},
			{Feedback: feedback.Feedback{Key: "note"}}, // So now it's not all correctness or all note
		}
		config := feedback.GetDefaultFeedbackConfig(feedbacks)

		assert.Equal(t, feedback.FeedbackTypeContinuous, config.Type)
		assert.Nil(t, config.Min)
		assert.Nil(t, config.Max)
		assert.Empty(t, config.Categories)
	})
}

func TestVerifyFeedbackConfig(t *testing.T) {
	t.Run("continuous: valid numeric score within range", func(t *testing.T) {
		f := feedback.Feedback{
			Score: float64(0.5),
		}
		cfg := feedback.FeedbackConfig{
			Type: feedback.FeedbackTypeContinuous,
			Min:  util.FloatPtr(0),
			Max:  util.FloatPtr(1),
		}
		err := feedback.VerifyFeedbackConfig(f, cfg)
		assert.NoError(t, err)
	})

	t.Run("continuous: non-numeric score -> error", func(t *testing.T) {
		f := feedback.Feedback{
			Score: "abc",
		}
		cfg := feedback.FeedbackConfig{
			Type: feedback.FeedbackTypeContinuous,
			Min:  util.FloatPtr(0),
			Max:  util.FloatPtr(1),
		}
		err := feedback.VerifyFeedbackConfig(f, cfg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "must be a number")
	})

	t.Run("continuous: score < min -> error", func(t *testing.T) {
		f := feedback.Feedback{
			Score: float64(-1),
		}
		cfg := feedback.FeedbackConfig{
			Type: feedback.FeedbackTypeContinuous,
			Min:  util.FloatPtr(0),
			Max:  util.FloatPtr(1),
		}
		err := feedback.VerifyFeedbackConfig(f, cfg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "is less than minimum")
	})

	t.Run("continuous: score > max -> error", func(t *testing.T) {
		f := feedback.Feedback{
			Score: float64(2),
		}
		cfg := feedback.FeedbackConfig{
			Type: feedback.FeedbackTypeContinuous,
			Min:  util.FloatPtr(0),
			Max:  util.FloatPtr(1),
		}
		err := feedback.VerifyFeedbackConfig(f, cfg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "is greater than maximum")
	})

	t.Run("categorical: valid category", func(t *testing.T) {
		f := feedback.Feedback{
			Score: float64(1),
		}
		cfg := feedback.FeedbackConfig{
			Type: feedback.FeedbackTypeCategorical,
			Categories: []feedback.FeedbackCategory{
				{Value: 0, Label: util.StringPtr("Bad")},
				{Value: 1, Label: util.StringPtr("Good")},
			},
		}
		err := feedback.VerifyFeedbackConfig(f, cfg)
		assert.NoError(t, err)
	})

	t.Run("categorical: invalid category -> error", func(t *testing.T) {
		f := feedback.Feedback{
			Score: float64(2),
		}
		cfg := feedback.FeedbackConfig{
			Type: feedback.FeedbackTypeCategorical,
			Categories: []feedback.FeedbackCategory{
				{Value: 0, Label: util.StringPtr("Bad")},
				{Value: 1, Label: util.StringPtr("Good")},
			},
		}
		err := feedback.VerifyFeedbackConfig(f, cfg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "is not a valid category")
	})

	t.Run("freeform (or other type) -> no constraints, no error", func(t *testing.T) {
		f := feedback.Feedback{
			Score: "any possible data",
		}
		cfg := feedback.FeedbackConfig{
			Type: feedback.FeedbackTypeFreeform,
		}
		err := feedback.VerifyFeedbackConfig(f, cfg)
		assert.NoError(t, err)
	})
}

func TestResolveFeedbackConfig(t *testing.T) {
	makeConfig := func(tpe feedback.FeedbackType, min, max float64) feedback.FeedbackConfig {
		return feedback.FeedbackConfig{
			Type: tpe,
			Min:  &min,
			Max:  &max,
		}
	}

	stored := makeConfig(feedback.FeedbackTypeCategorical, 0, 0)
	payload := makeConfig(feedback.FeedbackTypeCategorical, 0, 0)
	def := makeConfig(feedback.FeedbackTypeContinuous, 0, 1)

	t.Run("storedConfig not nil and matches payloadConfig -> return storedConfig", func(t *testing.T) {
		res, err := feedback.ResolveFeedbackConfig(&stored, &payload, def)
		assert.NoError(t, err)
		assert.Equal(t, stored, res)
	})

	t.Run("storedConfig not nil but payloadConfig differs -> error", func(t *testing.T) {
		diffPayload := makeConfig(feedback.FeedbackTypeFreeform, 0, 0)
		res, err := feedback.ResolveFeedbackConfig(&stored, &diffPayload, def)
		assert.Error(t, err)
		assert.Empty(t, res)
		assert.Contains(t, err.Error(), "feedback config mismatch")
	})

	t.Run("storedConfig nil, payloadConfig not nil -> return payloadConfig", func(t *testing.T) {
		res, err := feedback.ResolveFeedbackConfig(nil, &payload, def)
		assert.NoError(t, err)
		assert.Equal(t, payload, res)
	})

	t.Run("storedConfig nil, payloadConfig nil -> return defaultConfig", func(t *testing.T) {
		res, err := feedback.ResolveFeedbackConfig(nil, nil, def)
		assert.NoError(t, err)
		assert.Equal(t, def, res)
	})
}

func TestValidateFeedbackConfig(t *testing.T) {
	t.Run("continuous: valid config (no categories, no error)", func(t *testing.T) {
		cfg := feedback.FeedbackConfig{
			Type: feedback.FeedbackTypeContinuous,
			Min:  util.FloatPtr(0),
			Max:  util.FloatPtr(1),
			// No categories
		}
		err := feedback.ValidateFeedbackConfig(cfg)
		assert.NoError(t, err, "expected no error for valid continuous config")
	})

	t.Run("continuous: min >= max => error", func(t *testing.T) {
		cfg := feedback.FeedbackConfig{
			Type: feedback.FeedbackTypeContinuous,
			Min:  util.FloatPtr(1),
			Max:  util.FloatPtr(1),
		}
		err := feedback.ValidateFeedbackConfig(cfg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "min must be less than max")
	})

	t.Run("continuous: categories with duplicate labels => error", func(t *testing.T) {
		cfg := feedback.FeedbackConfig{
			Type: feedback.FeedbackTypeContinuous,
			Min:  util.FloatPtr(0),
			Max:  util.FloatPtr(5),
			Categories: []feedback.FeedbackCategory{
				{Value: 1, Label: util.StringPtr("One")},
				{Value: 2, Label: util.StringPtr("Two")},
				{Value: 3, Label: util.StringPtr("Two")}, // Duplicate label
			},
		}
		err := feedback.ValidateFeedbackConfig(cfg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "category labels must be unique")
	})

	t.Run("continuous: categories with duplicate values => error", func(t *testing.T) {
		cfg := feedback.FeedbackConfig{
			Type: feedback.FeedbackTypeContinuous,
			Min:  util.FloatPtr(0),
			Max:  util.FloatPtr(5),
			Categories: []feedback.FeedbackCategory{
				{Value: 1, Label: util.StringPtr("One")},
				{Value: 1, Label: util.StringPtr("DuplicateValue")},
			},
		}
		err := feedback.ValidateFeedbackConfig(cfg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "category values must be unique")
	})

	t.Run("continuous: category value outside [min, max] => error", func(t *testing.T) {
		cfg := feedback.FeedbackConfig{
			Type: feedback.FeedbackTypeContinuous,
			Min:  util.FloatPtr(0),
			Max:  util.FloatPtr(5),
			Categories: []feedback.FeedbackCategory{
				{Value: -1, Label: util.StringPtr("Negative")}, // < min
			},
		}
		err := feedback.ValidateFeedbackConfig(cfg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "category values must be greater than or equal to min")
	})

	t.Run("categorical: valid config with 2+ categories => no error", func(t *testing.T) {
		cfg := feedback.FeedbackConfig{
			Type: feedback.FeedbackTypeCategorical,
			Categories: []feedback.FeedbackCategory{
				{Value: 0, Label: util.StringPtr("Bad")},
				{Value: 1, Label: util.StringPtr("Good")},
			},
		}
		err := feedback.ValidateFeedbackConfig(cfg)
		assert.NoError(t, err)
	})

	t.Run("categorical: no categories => error", func(t *testing.T) {
		cfg := feedback.FeedbackConfig{
			Type:       feedback.FeedbackTypeCategorical,
			Categories: []feedback.FeedbackCategory{},
		}
		err := feedback.ValidateFeedbackConfig(cfg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "categories must not be empty for categorical feedback")
	})

	t.Run("categorical: only 1 category => error", func(t *testing.T) {
		cfg := feedback.FeedbackConfig{
			Type: feedback.FeedbackTypeCategorical,
			Categories: []feedback.FeedbackCategory{
				{Value: 1, Label: util.StringPtr("Only One")},
			},
		}
		err := feedback.ValidateFeedbackConfig(cfg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "must have at least 2 values")
	})

	t.Run("categorical: min or max specified => error", func(t *testing.T) {
		cfg := feedback.FeedbackConfig{
			Type: feedback.FeedbackTypeCategorical,
			Min:  util.FloatPtr(0),
			Max:  util.FloatPtr(1),
			Categories: []feedback.FeedbackCategory{
				{Value: 0, Label: util.StringPtr("A")},
				{Value: 1, Label: util.StringPtr("B")},
			},
		}
		err := feedback.ValidateFeedbackConfig(cfg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "min and max should not be specified for categorical feedback")
	})

	t.Run("freeform: valid (no categories, no min/max)", func(t *testing.T) {
		cfg := feedback.FeedbackConfig{
			Type: feedback.FeedbackTypeFreeform,
			// All other fields are default (nil or empty)
		}
		err := feedback.ValidateFeedbackConfig(cfg)
		assert.NoError(t, err)
	})

	t.Run("freeform: min, max, or categories => error", func(t *testing.T) {
		cfg := feedback.FeedbackConfig{
			Type: feedback.FeedbackTypeFreeform,
			Min:  util.FloatPtr(0),
			Categories: []feedback.FeedbackCategory{
				{Value: 1, Label: util.StringPtr("Should not exist")},
			},
		}
		err := feedback.ValidateFeedbackConfig(cfg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "should not be specified for freeform feedback")
	})

	t.Run("unrecognized feedback type => error", func(t *testing.T) {
		cfg := feedback.FeedbackConfig{
			Type: "unknown-type",
		}
		err := feedback.ValidateFeedbackConfig(cfg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "unrecognized feedback type")
	})
}
