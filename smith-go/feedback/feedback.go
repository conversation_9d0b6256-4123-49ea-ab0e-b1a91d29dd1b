package feedback

import (
	"errors"
	"fmt"
	"reflect"
	"strings"
	"time"

	"github.com/google/uuid"
	"golang.org/x/text/unicode/norm"
	"langchain.com/smith/util"
)

type Feedback struct {
	RunID                   *uuid.UUID      `json:"run_id"`
	SessionID               *uuid.UUID      `json:"session_id"`
	CreatedAt               *time.Time      `json:"created_at"`
	ModifiedAt              *time.Time      `json:"modified_at"`
	Key                     string          `json:"key"`
	Score                   interface{}     `json:"score"`
	Value                   interface{}     `json:"value"`
	Comment                 *string         `json:"comment"`
	Correction              interface{}     `json:"correction"`
	FeedbackGroupID         *uuid.UUID      `json:"feedback_group_id"`
	ComparativeExperimentID *uuid.UUID      `json:"comparative_experiment_id"`
	FeedbackConfig          *FeedbackConfig `json:"feedback_config"`
}

type FeedbackCreateSchema struct {
	Feedback
	ID             *uuid.UUID      `json:"id,omitempty"`
	TraceID        *uuid.UUID      `json:"trace_id,omitempty"`
	FeedbackSource *FeedbackSource `json:"feedback_source,omitempty"`
	Error          *bool           `json:"error"`
}

type FeedbackSource struct {
	Type     *string                `json:"type,omitempty"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

const (
	FeedbackSourceApp      = "app"
	FeedbackSourceAPI      = "api"
	FeedbackSourceModel    = "model"
	FeedbackSourceAutoEval = "auto_eval"
)

type FeedbackConfig struct {
	Type       FeedbackType       `json:"type"`
	Min        *float64           `json:"min,omitempty"`
	Max        *float64           `json:"max,omitempty"`
	Categories []FeedbackCategory `json:"categories,omitempty"`
}

type FeedbackType string

const (
	FeedbackTypeContinuous  FeedbackType = "continuous"
	FeedbackTypeCategorical FeedbackType = "categorical"
	FeedbackTypeFreeform    FeedbackType = "freeform"
)

type FeedbackCategory struct {
	Value float64 `json:"value"`
	Label *string `json:"label,omitempty" validate:"min=1"`
}

var ErrInvalidFeedbackKey = errors.New("invalid feedback key")

func GetDefaultFeedbackConfig(feedbacks []FeedbackCreateSchema) FeedbackConfig {
	if len(feedbacks) == 0 {
		return FeedbackConfig{Type: FeedbackTypeContinuous}
	}

	allCorrectnessWithScore := true
	allNote := true

	for _, f := range feedbacks {
		normalized, err := normalizeFeedbackKey(f.Key)
		if err != nil {
			allCorrectnessWithScore = false
			allNote = false
			break
		}
		if normalized != "correctness" || f.Score == nil {
			allCorrectnessWithScore = false
		}
		if normalized != "note" {
			allNote = false
		}
	}

	if allCorrectnessWithScore {
		return FeedbackConfig{
			Type: FeedbackTypeContinuous,
			Min:  util.FloatPtr(0),
			Max:  util.FloatPtr(1),
			Categories: []FeedbackCategory{
				{Value: 0},
				{Value: 1},
			},
		}
	}

	if allNote {
		return FeedbackConfig{
			Type: FeedbackTypeFreeform,
		}
	}

	return FeedbackConfig{
		Type: FeedbackTypeContinuous,
	}
}

func VerifyFeedbackConfig(f Feedback, config FeedbackConfig) error {
	switch config.Type {
	case FeedbackTypeContinuous:
		if f.Score != nil {
			val, ok := f.Score.(float64)
			if !ok {
				return fmt.Errorf("feedback score must be a number")
			}
			if val < *config.Min {
				return fmt.Errorf("feedback score %v is less than minimum %v", val, *config.Min)
			}
			if config.Max != nil && val > *config.Max {
				return fmt.Errorf("feedback score %v is greater than maximum %v", val, *config.Max)
			}
		}

	case FeedbackTypeCategorical:
		if f.Score != nil {
			val, ok := f.Score.(float64)
			if !ok {
				return fmt.Errorf("feedback score must be a number")
			}
			found := false
			for _, cat := range config.Categories {
				if cat.Value == val {
					found = true
					break
				}
			}
			if !found {
				return fmt.Errorf("feedback score %v is not a valid category", val)
			}
		}
	}

	return nil
}

func ResolveFeedbackConfig(
	storedConfig *FeedbackConfig,
	payloadConfig *FeedbackConfig,
	defaultConfig FeedbackConfig,
) (FeedbackConfig, error) {
	if storedConfig != nil {
		if payloadConfig != nil && !reflect.DeepEqual(*storedConfig, *payloadConfig) {
			return FeedbackConfig{}, fmt.Errorf(
				"feedback config mismatch: payload=%v != stored=%v",
				payloadConfig, storedConfig,
			)
		}
		return *storedConfig, nil
	}

	if payloadConfig != nil {
		return *payloadConfig, nil
	}

	return defaultConfig, nil
}

func normalizeFeedbackKey(key string) (string, error) {
	if key == "" {
		return "", fmt.Errorf("%w: feedback key must be a non-empty string", ErrInvalidFeedbackKey)
	}
	normalized := norm.NFC.String(key)
	return strings.ToLower(strings.TrimSpace(normalized)), nil
}

func ValidateFeedbackConfig(cfg FeedbackConfig) error {
	switch cfg.Type {
	case FeedbackTypeContinuous:
		if cfg.Min != nil && cfg.Max != nil && *cfg.Min >= *cfg.Max {
			return errors.New("min must be less than max")
		}

		// If there are categories, check:
		//   - unique labels
		//   - unique values
		//   - category value >= Min (if Min != nil)
		//   - category value <= Max (if Max != nil)
		if len(cfg.Categories) > 0 {
			// Check that category labels are unique
			labelSet := make(map[string]struct{})
			for _, cat := range cfg.Categories {
				if cat.Label == nil {
					continue
				}
				if _, exists := labelSet[*cat.Label]; exists {
					return errors.New("category labels must be unique")
				}
				labelSet[*cat.Label] = struct{}{}
			}

			// Check that category values are unique
			valueSet := make(map[float64]struct{})
			for _, cat := range cfg.Categories {
				if _, exists := valueSet[cat.Value]; exists {
					return errors.New("category values must be unique")
				}
				valueSet[cat.Value] = struct{}{}
			}

			// Check category value range
			for _, cat := range cfg.Categories {
				if cfg.Min != nil && cat.Value < *cfg.Min {
					return errors.New("category values must be greater than or equal to min")
				}
				if cfg.Max != nil && cat.Value > *cfg.Max {
					return errors.New("category values must be less than or equal to max")
				}
			}
		}

	case FeedbackTypeCategorical:
		if len(cfg.Categories) == 0 {
			return errors.New("categories must not be empty for categorical feedback")
		}
		if len(cfg.Categories) < 2 {
			return errors.New("categories must have at least 2 values for categorical feedback")
		}
		if cfg.Min != nil || cfg.Max != nil {
			return errors.New("min and max should not be specified for categorical feedback")
		}

		// Check uniqueness of category values
		valueSet := make(map[float64]struct{})
		for _, cat := range cfg.Categories {
			if _, exists := valueSet[cat.Value]; exists {
				return errors.New("category values must be unique")
			}
			valueSet[cat.Value] = struct{}{}
		}

		// Check uniqueness of category labels (if set)
		labelSet := make(map[string]struct{})
		for _, cat := range cfg.Categories {
			if cat.Label == nil {
				continue
			}
			if _, exists := labelSet[*cat.Label]; exists {
				return errors.New("category labels must be unique")
			}
			labelSet[*cat.Label] = struct{}{}
		}

	case FeedbackTypeFreeform:
		if cfg.Min != nil || cfg.Max != nil || len(cfg.Categories) > 0 {
			return errors.New("min, max and categories should not be specified for freeform feedback")
		}

	default:
		return fmt.Errorf("unrecognized feedback type: %s", cfg.Type)
	}

	return nil
}
