package testutil

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"

	"langchain.com/smith/auth"
	"langchain.com/smith/util"
)

// Replace with this when hopefully this gets merged: https://github.com/stretchr/testify/pull/1287/files
func StringPtrEqual(t *testing.T, expected *string, actual *string, msg string) {
	if expected == nil && actual == nil {
		return
	}
	if expected == nil {
		t.Errorf("%s: expected nil, got %q", msg, *actual)
		return
	}
	if actual == nil {
		t.Errorf("%s: expected %q, got nil", msg, *expected)
		return
	}
	assert.Equal(t, *expected, *actual, fmt.Sprintf("%s: expected %q, got %q", msg, *expected, *actual))
}

func GetDefaultTenantConfig() *auth.TenantConfig {
	maxHourlyTracingRequests := 105000
	maxHourlyTracingBytes := uint64(2000000000)
	maxMonthlyTotalUniqueTraces := uint64(10000000)
	maxEventsIngestedPerMinute := 20000
	maxRunRules := 100

	return &auth.TenantConfig{
		MaxHourlyTracingRequests:    maxHourlyTracingRequests,
		MaxHourlyTracingBytes:       maxHourlyTracingBytes,
		MaxMonthlyTotalUniqueTraces: maxMonthlyTotalUniqueTraces,
		MaxEventsIngestedPerMinute:  maxEventsIngestedPerMinute,
		MaxRunRules:                 maxRunRules,
		OrganizationConfig:          GetDefaultOrgConfig(),
	}
}

func GetDefaultOrgConfig() *auth.OrganizationConfig {
	return &auth.OrganizationConfig{
		MaxIdentities:           5,
		MaxWorkspaces:           1,
		CanAddSeats:             util.BoolPtr(true),
		KvDatasetMessageSupport: util.BoolPtr(true),
		CanUseLanggraphCloud:    util.BoolPtr(true),

		CanUseRbac:                       util.BoolPtr(false),
		CanDisablePublicSharing:          util.BoolPtr(false),
		CanServeDatasets:                 util.BoolPtr(false),
		MaxLanggraphCloudDeployments:     3,
		MaxFreeLanggraphCloudDeployments: 0,
		CanUseSamlSso:                    util.BoolPtr(false),
		CanUseBulkExport:                 util.BoolPtr(false),
		DemoLgpNewGraphEnabled:           util.BoolPtr(false),
		ShowUpdatedSidenav:               util.BoolPtr(false),
		ShowUpdatedResourceTags:          util.BoolPtr(false),
		ShowPlaygroundPromptCanvas:       util.BoolPtr(false),
		AllowCustomIframes:               util.BoolPtr(false),
		EnableLanggraphPricing:           util.BoolPtr(false),
		EnableThreadViewPlayground:       util.BoolPtr(false),
		EnableOrgUsageCharts:             util.BoolPtr(false),
		EnableSelectAllTraces:            util.BoolPtr(false),
		LangGraphDeployOwnCloudEnabled:   util.BoolPtr(false),
		EnableK8sVanillaPlatform:         util.BoolPtr(false),
		LangGraphRemoteReconcilerEnabled: util.BoolPtr(false),
		LgpTemplatesEnabled:              util.BoolPtr(false),
		EnablePrebuiltDashboards:         util.BoolPtr(false),
		LangsmithAlertsPocEnabled:        util.BoolPtr(true),
		LangGraphPlatformGAEnabled:       util.BoolPtr(false),
		DatadogRumSessionSampleRate:      20,
		ExperimentalSearchEnabled:        util.BoolPtr(false),
		EnableAlignEvaluators:            util.BoolPtr(false),
		PlaygroundEvaluatorStrategy:      util.StringPtr("cron"),
		EnableMonthlyUsageCharts:         util.BoolPtr(false),
	}
}

func AssertAuditLogVarsInQuery(t *testing.T, sql string, operation_name string, org_id string, tenant_id string, user_id *string, ls_user_id *string, api_key *string) {
	// These should always be present
	assert.Contains(t, sql, fmt.Sprintf("-- audit_operation_name:%s", operation_name), "Expected audit log var audit_operation_name not found in query")
	assert.Contains(t, sql, fmt.Sprintf("-- organization_id:%s", org_id), "Expected audit log var organization_id not found in query")
	assert.Contains(t, sql, fmt.Sprintf("-- tenant_id:%s", tenant_id), "Expected audit log var tenant_id not found in query")

	// These are optionally present depending on whether the operation is using a JWT or API key
	if user_id != nil {
		assert.Contains(t, sql, fmt.Sprintf("-- user_id:%s", *user_id), "Expected audit log var user_id not found in query")
	}
	if ls_user_id != nil {
		assert.Contains(t, sql, fmt.Sprintf("-- ls_user_id:%s", *ls_user_id), "Expected audit log var ls_user_id not found in query")
	}
	if api_key != nil {
		assert.Contains(t, sql, fmt.Sprintf("-- api_key_short:%s", *api_key), "Expected audit log var api_key not found in query")
	}
}
